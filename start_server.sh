#!/bin/bash

# TDSQL AI测试平台启动脚本
# 支持跨网络访问

echo "=== TDSQL AI测试平台启动脚本 ==="
echo ""

# 获取本机IP地址
get_local_ip() {
    # 尝试多种方法获取本机IP
    local ip=""
    
    # 方法1: 使用hostname -I (Linux)
    if command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 方法2: 使用ifconfig (macOS/Linux)
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig | grep -E "inet.*broadcast" | awk '{print $2}' | head -1)
    fi
    
    # 方法3: 使用ip命令 (Linux)
    if [ -z "$ip" ] && command -v ip >/dev/null 2>&1; then
        ip=$(ip route get ******* | awk '{print $7; exit}')
    fi
    
    # 默认使用localhost
    if [ -z "$ip" ]; then
        ip="localhost"
    fi
    
    echo "$ip"
}

LOCAL_IP=$(get_local_ip)

echo "检测到本机IP地址: $LOCAL_IP"
echo ""

# 前端现在会自动检测API URL，无需手动更新配置文件
echo "前端已配置为自动检测API URL，支持跨网络访问"

echo "启动信息:"
echo "- 后端服务地址: http://$LOCAL_IP:8000"
echo "- 前端服务地址: http://$LOCAL_IP:3000"
echo "- API文档地址: http://$LOCAL_IP:8000/docs"
echo ""

echo "可用的测试账号:"
echo "- admin / admin123"
echo "- test / test123"
echo "- user / user123"
echo "- demo / demo123"
echo "- guest / guest123"
echo ""

echo "同事可以使用以下链接访问:"
echo "http://$LOCAL_IP:3000"
echo ""

# 启动后端服务
echo "正在启动后端服务..."
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务
echo "正在启动前端服务..."
cd ../frontend
npm run dev -- --host 0.0.0.0 --port 3000 &
FRONTEND_PID=$!

echo ""
echo "服务启动完成！"
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
