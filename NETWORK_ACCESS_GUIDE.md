# TDSQL AI测试平台 - 跨网络访问指南

## 问题解决方案

本指南解决了同事无法通过网络链接登录的权限问题。

## 已修复的问题

### 1. CORS跨域问题
- **问题**: 后端CORS配置限制了访问来源
- **解决**: 更新配置允许所有来源访问
- **文件**: `.env`, `backend/app/config/settings.py`

### 2. 前端API URL硬编码问题
- **问题**: 前端API URL固定为localhost，无法跨网络访问
- **解决**: 实现动态API URL配置
- **文件**: `frontend/src/services/api.js`, `frontend/.env`

### 3. 登录验证过于严格
- **问题**: 只有admin/admin123可以登录
- **解决**: 添加多个测试账号
- **文件**: `backend/app/api/v1/auth.py`

## 快速启动

### 方法1: 使用启动脚本（推荐）
```bash
./start_server.sh
```

这个脚本会：
- 自动检测本机IP地址
- 更新前端配置
- 启动后端和前端服务
- 显示访问链接

### 方法2: 手动启动

1. **启动后端**:
```bash
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

2. **启动前端**:
```bash
cd frontend
npm run dev -- --host 0.0.0.0 --port 3000
```

3. **更新前端配置**（如果需要跨网络访问）:
编辑 `frontend/.env` 文件：
```
VITE_API_URL=http://YOUR_IP_ADDRESS:8000/api/v1
```

## 测试账号

现在支持以下测试账号：
- `admin` / `admin123`
- `test` / `test123`
- `user` / `user123`
- `demo` / `demo123`
- `guest` / `guest123`

## 访问地址

假设你的IP地址是 `*************`：

- **前端访问**: http://*************:3000
- **后端API**: http://*************:8000
- **API文档**: http://*************:8000/docs

## 网络要求

1. **防火墙设置**: 确保端口3000和8000对外开放
2. **网络连通性**: 同事的电脑需要能够访问你的IP地址
3. **同一网络**: 建议在同一局域网内使用

## 故障排除

### 1. 无法访问前端
- 检查防火墙是否阻止了3000端口
- 确认前端服务是否以 `--host 0.0.0.0` 启动

### 2. API请求失败
- 检查后端服务是否正常运行
- 确认CORS配置是否正确
- 检查前端的API URL配置

### 3. 登录失败
- 确认使用的是支持的测试账号
- 检查网络连接
- 查看浏览器开发者工具的网络请求

## 生产环境部署

对于生产环境，建议：

1. **使用反向代理**: 如Nginx
2. **HTTPS配置**: 启用SSL证书
3. **域名配置**: 使用域名而不是IP地址
4. **安全加固**: 限制CORS来源，使用真实的用户认证系统

## 配置文件说明

- `.env`: 后端环境变量
- `frontend/.env`: 前端开发环境变量
- `frontend/.env.production`: 前端生产环境变量
- `start_server.sh`: 自动启动脚本
