import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import api from '../services/api'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // 状态
      isAuthenticated: false,
      user: null,
      token: null,
      
      // 登录
      login: async (credentials) => {
        try {
          // 第一步：登录获取token
          const response = await api.post('/auth/login', credentials)
          const { access_token, token_type } = response.data

          // 第二步：获取用户信息
          try {
            const userResponse = await api.get('/auth/me', {
              headers: { Authorization: `${token_type} ${access_token}` }
            })

            // 只有在成功获取用户信息后才设置认证状态
            set({
              isAuthenticated: true,
              token: access_token,
              user: userResponse.data
            })

            return { success: true }
          } catch (userError) {
            console.error('获取用户信息失败:', userError)
            // 如果获取用户信息失败，仍然设置基本的认证状态
            set({
              isAuthenticated: true,
              token: access_token,
              user: {
                username: credentials.username,
                email: '<EMAIL>',
                full_name: credentials.username,
                is_active: true
              }
            })

            return { success: true }
          }
        } catch (error) {
          // 清除任何可能的认证状态
          set({
            isAuthenticated: false,
            token: null,
            user: null
          })

          return {
            success: false,
            message: error.response?.data?.detail || error.response?.data?.message || '登录失败'
          }
        }
      },
      
      // 注册
      register: async (userData) => {
        try {
          await api.post('/auth/register', userData)
          return { success: true, message: '注册成功' }
        } catch (error) {
          return { 
            success: false, 
            message: error.response?.data?.message || '注册失败' 
          }
        }
      },
      
      // 登出
      logout: async () => {
        try {
          await api.post('/auth/logout')
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({ 
            isAuthenticated: false, 
            user: null, 
            token: null 
          })
        }
      },
      
      // 更新用户信息
      updateUser: (userData) => {
        set({ user: { ...get().user, ...userData } })
      },
      
      // 检查token有效性
      checkAuth: async () => {
        const { token } = get()
        if (!token) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }
        
        try {
          const response = await api.get('/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          })
          set({ user: response.data, isAuthenticated: true })
          return true
        } catch (error) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token
      })
    }
  )
)
