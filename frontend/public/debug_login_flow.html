<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录流程调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 登录流程调试页面</h1>
        <p>这个页面可以帮助调试注册登录流程中的问题</p>
    </div>

    <div class="container">
        <h2>📋 步骤1: 测试API连接</h2>
        <button class="btn" onclick="testApiConnection()">测试API连接</button>
        <div id="apiResult"></div>
    </div>

    <div class="container">
        <h2>📝 步骤2: 用户注册</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="regUsername" value="testuser123" placeholder="输入用户名">
        </div>
        <div class="form-group">
            <label>邮箱:</label>
            <input type="email" id="regEmail" value="<EMAIL>" placeholder="输入邮箱">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="regPassword" value="testpass123" placeholder="输入密码">
        </div>
        <div class="form-group">
            <label>姓名:</label>
            <input type="text" id="regFullName" value="测试用户" placeholder="输入姓名（可选）">
        </div>
        <button class="btn" onclick="testRegister()">注册用户</button>
        <div id="registerResult"></div>
    </div>

    <div class="container">
        <h2>🔑 步骤3: 用户登录</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="loginUsername" value="testuser123" placeholder="输入用户名">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="loginPassword" value="testpass123" placeholder="输入密码">
        </div>
        <button class="btn" onclick="testLogin()">登录</button>
        <div id="loginResult"></div>
    </div>

    <div class="container">
        <h2>👤 步骤4: 获取用户信息</h2>
        <button class="btn" onclick="testGetUserInfo()">获取用户信息</button>
        <div id="userInfoResult"></div>
    </div>

    <div class="container">
        <h2>🧪 步骤5: 测试默认用户</h2>
        <button class="btn" onclick="testDefaultLogin()">测试默认用户登录 (admin/admin123)</button>
        <div id="defaultLoginResult"></div>
    </div>

    <div class="container">
        <h2>📊 当前状态</h2>
        <button class="btn success" onclick="showCurrentState()">显示当前状态</button>
        <div id="currentState"></div>
    </div>

    <script>
        let currentToken = null;
        
        // 获取API基础URL
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return `${protocol}//${hostname}:8000/api/v1`;
            }
            return `${protocol}//${hostname}:8000/api/v1`;
        }

        // 添加结果显示
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 清空结果
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 测试API连接
        async function testApiConnection() {
            clearResults('apiResult');
            const apiUrl = getApiBaseUrl();
            const healthUrl = apiUrl.replace('/api/v1', '/health');
            
            addResult('apiResult', `正在测试API连接: ${healthUrl}`, 'info');
            
            try {
                const response = await fetch(healthUrl);
                if (response.ok) {
                    const data = await response.json();
                    addResult('apiResult', `✅ API连接成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('apiResult', `❌ API连接失败！HTTP状态: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('apiResult', `❌ API连接失败！错误: ${error.message}`, 'error');
            }
        }

        // 测试注册
        async function testRegister() {
            clearResults('registerResult');
            const apiUrl = getApiBaseUrl();
            const registerUrl = `${apiUrl}/auth/register`;
            
            const userData = {
                username: document.getElementById('regUsername').value,
                email: document.getElementById('regEmail').value,
                password: document.getElementById('regPassword').value,
                full_name: document.getElementById('regFullName').value
            };
            
            addResult('registerResult', `正在注册用户: ${userData.username}`, 'info');
            
            try {
                const response = await fetch(registerUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('registerResult', `✅ 注册成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                    // 自动填充登录表单
                    document.getElementById('loginUsername').value = userData.username;
                    document.getElementById('loginPassword').value = userData.password;
                } else {
                    addResult('registerResult', `❌ 注册失败！\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('registerResult', `❌ 注册失败！错误: ${error.message}`, 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            clearResults('loginResult');
            const apiUrl = getApiBaseUrl();
            const loginUrl = `${apiUrl}/auth/login`;
            
            const credentials = {
                username: document.getElementById('loginUsername').value,
                password: document.getElementById('loginPassword').value
            };
            
            addResult('loginResult', `正在登录用户: ${credentials.username}`, 'info');
            
            try {
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.access_token;
                    addResult('loginResult', `✅ 登录成功！\nToken: ${data.access_token.substring(0, 50)}...\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('loginResult', `❌ 登录失败！\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('loginResult', `❌ 登录失败！错误: ${error.message}`, 'error');
            }
        }

        // 测试获取用户信息
        async function testGetUserInfo() {
            clearResults('userInfoResult');
            
            if (!currentToken) {
                addResult('userInfoResult', '❌ 请先登录获取Token', 'error');
                return;
            }
            
            const apiUrl = getApiBaseUrl();
            const userInfoUrl = `${apiUrl}/auth/me`;
            
            addResult('userInfoResult', '正在获取用户信息...', 'info');
            
            try {
                const response = await fetch(userInfoUrl, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('userInfoResult', `✅ 获取用户信息成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('userInfoResult', `❌ 获取用户信息失败！\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('userInfoResult', `❌ 获取用户信息失败！错误: ${error.message}`, 'error');
            }
        }

        // 测试默认用户登录
        async function testDefaultLogin() {
            clearResults('defaultLoginResult');
            const apiUrl = getApiBaseUrl();
            const loginUrl = `${apiUrl}/auth/login`;
            
            const credentials = {
                username: 'admin',
                password: 'admin123'
            };
            
            addResult('defaultLoginResult', '正在测试默认用户登录...', 'info');
            
            try {
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(credentials)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentToken = data.access_token;
                    addResult('defaultLoginResult', `✅ 默认用户登录成功！\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult('defaultLoginResult', `❌ 默认用户登录失败！\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('defaultLoginResult', `❌ 默认用户登录失败！错误: ${error.message}`, 'error');
            }
        }

        // 显示当前状态
        function showCurrentState() {
            clearResults('currentState');
            
            const state = {
                'API URL': getApiBaseUrl(),
                '当前页面': window.location.href,
                '当前Token': currentToken ? `${currentToken.substring(0, 50)}...` : '无',
                'LocalStorage': localStorage.getItem('auth-storage') || '无',
                '浏览器信息': navigator.userAgent
            };
            
            addResult('currentState', JSON.stringify(state, null, 2), 'info');
        }

        // 页面加载时显示基本信息
        window.onload = function() {
            showCurrentState();
        };
    </script>
</body>
</html>
