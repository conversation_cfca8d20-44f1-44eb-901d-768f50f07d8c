<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API URL 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .api-url {
            font-weight: bold;
            color: #007bff;
            font-size: 18px;
        }
        .test-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #218838;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API URL 调试页面</h1>
        
        <div class="info-item">
            <strong>当前页面URL:</strong> <span id="currentUrl"></span>
        </div>
        
        <div class="info-item">
            <strong>主机名:</strong> <span id="hostname"></span>
        </div>
        
        <div class="info-item">
            <strong>协议:</strong> <span id="protocol"></span>
        </div>
        
        <div class="info-item">
            <strong>端口:</strong> <span id="port"></span>
        </div>
        
        <div class="info-item">
            <strong>计算出的API URL:</strong> <span id="apiUrl" class="api-url"></span>
        </div>
        
        <div class="info-item">
            <strong>环境变量 VITE_API_URL:</strong> <span id="envApiUrl"></span>
        </div>
        
        <hr>
        
        <h3>🧪 API 连接测试</h3>
        <button class="test-button" onclick="testApiConnection()">测试 API 连接</button>
        <button class="test-button" onclick="testLogin()">测试登录接口</button>
        
        <div id="testResults"></div>
        
        <hr>
        
        <h3>📝 说明</h3>
        <ul>
            <li>如果你在本机访问 (localhost/127.0.0.1)，API URL 会使用环境变量或默认的 localhost:8000</li>
            <li>如果你从其他机器访问，API URL 会自动使用当前主机名的 8000 端口</li>
            <li>例如：访问 http://************:3000 时，API 会使用 http://************:8000</li>
        </ul>
    </div>

    <script>
        // 模拟前端的 API URL 获取逻辑
        function getApiBaseUrl() {
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            
            // 模拟环境变量（在实际应用中这会是 import.meta.env.VITE_API_URL）
            const envApiUrl = 'http://localhost:8000/api/v1';
            
            // 检查环境变量，但只在本地开发时使用localhost
            if (envApiUrl && (hostname === 'localhost' || hostname === '127.0.0.1')) {
                return envApiUrl;
            }
            
            // 对于跨网络访问，始终使用当前访问的主机名
            if (hostname === 'localhost' || hostname === '127.0.0.1') {
                return `${protocol}//${hostname}:8000/api/v1`;
            }
            
            // 其他情况（跨网络访问），使用当前主机名的8000端口
            return `${protocol}//${hostname}:8000/api/v1`;
        }

        // 页面加载时显示信息
        window.onload = function() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('hostname').textContent = window.location.hostname;
            document.getElementById('protocol').textContent = window.location.protocol;
            document.getElementById('port').textContent = window.location.port || '默认端口';
            document.getElementById('apiUrl').textContent = getApiBaseUrl();
            document.getElementById('envApiUrl').textContent = 'http://localhost:8000/api/v1 (模拟)';
        };

        // 测试 API 连接
        async function testApiConnection() {
            const apiUrl = getApiBaseUrl();
            const testUrl = apiUrl.replace('/api/v1', '/health');
            
            addResult('正在测试 API 连接...', 'info');
            
            try {
                const response = await fetch(testUrl);
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ API 连接成功！服务器响应: ${JSON.stringify(data)}`, 'success');
                } else {
                    addResult(`❌ API 连接失败！HTTP状态: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`❌ API 连接失败！错误: ${error.message}`, 'error');
            }
        }

        // 测试登录接口
        async function testLogin() {
            const apiUrl = getApiBaseUrl();
            const loginUrl = `${apiUrl}/auth/login`;
            
            addResult('正在测试登录接口...', 'info');
            
            try {
                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 登录接口测试成功！获得token: ${data.access_token.substring(0, 20)}...`, 'success');
                } else {
                    const errorData = await response.json();
                    addResult(`❌ 登录接口测试失败！错误: ${JSON.stringify(errorData)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 登录接口测试失败！错误: ${error.message}`, 'error');
            }
        }

        // 添加测试结果
        function addResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            
            // 滚动到底部
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
