#!/bin/bash

echo "🔄 重启前端服务以应用API URL修复..."

# 获取本机IP地址
get_local_ip() {
    local ip=""
    
    # 方法1: 使用hostname -I (Linux)
    if command -v hostname >/dev/null 2>&1; then
        ip=$(hostname -I 2>/dev/null | awk '{print $1}')
    fi
    
    # 方法2: 使用ifconfig (macOS/Linux)
    if [ -z "$ip" ] && command -v ifconfig >/dev/null 2>&1; then
        ip=$(ifconfig | grep -E "inet.*broadcast" | awk '{print $2}' | head -1)
    fi
    
    # 方法3: 使用ip命令 (Linux)
    if [ -z "$ip" ] && command -v ip >/dev/null 2>&1; then
        ip=$(ip route get ******* | awk '{print $7; exit}')
    fi
    
    # 默认使用localhost
    if [ -z "$ip" ]; then
        ip="localhost"
    fi
    
    echo "$ip"
}

LOCAL_IP=$(get_local_ip)

echo "检测到本机IP地址: $LOCAL_IP"

# 停止可能正在运行的前端服务
echo "正在停止前端服务..."
pkill -f "vite\|npm.*dev" 2>/dev/null || true
sleep 2

# 进入前端目录
cd frontend

# 清理缓存
echo "清理前端缓存..."
rm -rf node_modules/.vite 2>/dev/null || true
rm -rf dist 2>/dev/null || true

# 重新启动前端服务
echo "重新启动前端服务..."
echo "前端将在以下地址可用:"
echo "- 本机访问: http://localhost:3000"
echo "- 网络访问: http://$LOCAL_IP:3000"
echo ""
echo "API URL将自动检测:"
echo "- 本机访问时: http://localhost:8000/api/v1"
echo "- 网络访问时: http://$LOCAL_IP:8000/api/v1"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动前端开发服务器
npm run dev -- --host 0.0.0.0 --port 3000
