#!/usr/bin/env node

/**
 * 测试API URL修复的脚本
 * 模拟不同的访问场景
 */

// 模拟前端的API URL获取逻辑
function getApiBaseUrl(hostname, protocol = 'http:') {
    // 模拟环境变量
    const envApiUrl = 'http://localhost:8000/api/v1';
    
    // 检查环境变量，但只在本地开发时使用localhost
    if (envApiUrl && (hostname === 'localhost' || hostname === '127.0.0.1')) {
        return envApiUrl;
    }
    
    // 对于跨网络访问，始终使用当前访问的主机名
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return `${protocol}//${hostname}:8000/api/v1`;
    }
    
    // 其他情况（跨网络访问），使用当前主机名的8000端口
    return `${protocol}//${hostname}:8000/api/v1`;
}

// 测试不同的访问场景
const testCases = [
    {
        name: '本机localhost访问',
        hostname: 'localhost',
        expected: 'http://localhost:8000/api/v1'
    },
    {
        name: '本机127.0.0.1访问',
        hostname: '127.0.0.1',
        expected: 'http://localhost:8000/api/v1'
    },
    {
        name: '同事机器访问 (************)',
        hostname: '************',
        expected: 'http://************:8000/api/v1'
    },
    {
        name: '其他IP访问 (*************)',
        hostname: '*************',
        expected: 'http://*************:8000/api/v1'
    }
];

console.log('🧪 测试API URL自动检测功能\n');

let allPassed = true;

testCases.forEach((testCase, index) => {
    const result = getApiBaseUrl(testCase.hostname);
    const passed = result === testCase.expected;
    
    console.log(`${index + 1}. ${testCase.name}`);
    console.log(`   主机名: ${testCase.hostname}`);
    console.log(`   期望结果: ${testCase.expected}`);
    console.log(`   实际结果: ${result}`);
    console.log(`   测试结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (!passed) {
        allPassed = false;
    }
});

console.log('='.repeat(50));
if (allPassed) {
    console.log('🎉 所有测试通过！API URL自动检测功能正常工作。');
    console.log('\n📝 修复说明:');
    console.log('- 本机访问时使用 localhost:8000');
    console.log('- 跨网络访问时自动使用当前主机名:8000');
    console.log('- 同事从 ************:3000 访问时，API会自动使用 ************:8000');
} else {
    console.log('❌ 部分测试失败，请检查代码逻辑。');
    process.exit(1);
}

console.log('\n🔧 使用方法:');
console.log('1. 启动服务: ./start_server.sh');
console.log('2. 本机访问: http://localhost:3000');
console.log('3. 同事访问: http://你的IP:3000 (例如: http://************:3000)');
console.log('4. 调试页面: http://你的IP:3000/debug_api_url.html');
