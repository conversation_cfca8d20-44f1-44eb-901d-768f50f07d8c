# 🚨 立即修复指南 - API URL 问题

## 问题现状
前端代码已经修改，但是当前运行的前端服务还是旧版本，所以API请求仍然发送到 `localhost:8000`。

## 🔧 立即解决方案

### 方案1: 重启前端服务（推荐）

1. **停止当前前端服务**
   ```bash
   # 按 Ctrl+C 停止当前运行的前端服务
   # 或者强制停止
   pkill -f "vite\|npm.*dev"
   ```

2. **重新启动前端服务**
   ```bash
   ./restart_frontend.sh
   ```
   
   或者手动启动：
   ```bash
   cd frontend
   npm run dev -- --host 0.0.0.0 --port 3000
   ```

### 方案2: 强制刷新浏览器缓存

如果重启后还是有问题，让同事：
1. 按 `Ctrl+Shift+R` (Windows) 或 `Cmd+Shift+R` (Mac) 强制刷新
2. 或者按 `F12` 打开开发者工具，右键刷新按钮选择"清空缓存并硬性重新加载"

### 方案3: 临时手动修复（如果上述方案不行）

在浏览器控制台执行以下代码来临时修复API URL：
```javascript
// 在浏览器控制台执行
localStorage.clear();
window.location.reload();
```

## 🧪 验证修复

### 1. 检查API URL
让同事访问调试页面：
```
http://************:3000/debug_api_url.html
```

应该看到：
- **计算出的API URL**: `http://************:8000/api/v1` ✅
- **不应该是**: `http://localhost:8000/api/v1` ❌

### 2. 检查网络请求
在浏览器开发者工具的Network标签中，登录时应该看到：
- **Request URL**: `http://************:8000/api/v1/auth/login` ✅
- **不应该是**: `http://localhost:8000/api/v1/auth/login` ❌

## 🔍 详细排查步骤

### 步骤1: 确认前端服务重启
```bash
# 检查是否有前端服务在运行
ps aux | grep -E "(vite|npm.*dev)"

# 如果有，停止它们
pkill -f "vite\|npm.*dev"

# 重新启动
cd frontend
npm run dev -- --host 0.0.0.0 --port 3000
```

### 步骤2: 确认代码修改生效
检查 `frontend/src/services/api.js` 文件，确认包含以下代码：
```javascript
// 动态获取API基础URL
const getApiBaseUrl = () => {
  const protocol = window.location.protocol
  const hostname = window.location.hostname
  
  // 检查环境变量，但只在本地开发时使用localhost
  const envApiUrl = import.meta.env.VITE_API_URL
  if (envApiUrl && (hostname === 'localhost' || hostname === '127.0.0.1')) {
    return envApiUrl
  }
  
  // 其他情况（跨网络访问），使用当前主机名的8000端口
  return `${protocol}//${hostname}:8000/api/v1`
}
```

### 步骤3: 清理缓存
```bash
cd frontend
rm -rf node_modules/.vite
rm -rf dist
npm run dev -- --host 0.0.0.0 --port 3000
```

## 🎯 预期结果

修复后，同事访问 `http://************:3000` 时：

1. **调试页面显示**:
   - 主机名: `************`
   - 计算出的API URL: `http://************:8000/api/v1`

2. **网络请求**:
   - 登录请求发送到: `http://************:8000/api/v1/auth/login`
   - 不再是 `localhost`

3. **登录成功**:
   - 可以使用 `admin/admin123` 等测试账号登录
   - 可以注册新账号并登录

## 🚨 如果还是不行

### 检查后端服务
确认后端服务正常运行：
```bash
# 检查后端健康状态
curl http://************:8000/health

# 应该返回类似：
# {"status":"healthy","app_name":"TDSQL AI Testing Platform",...}
```

### 检查防火墙
确保8000端口对外开放：
```bash
# 测试端口连通性
telnet ************ 8000
```

### 检查CORS配置
确认 `.env` 文件中：
```
BACKEND_CORS_ORIGINS=*
```

## 📞 紧急联系

如果以上方案都不行，请：
1. 截图调试页面的显示内容
2. 截图浏览器开发者工具的Network标签
3. 提供具体的错误信息

## 🎉 成功标志

修复成功后，同事应该能够：
- ✅ 正常访问 `http://************:3000`
- ✅ 看到正确的API URL（不是localhost）
- ✅ 成功登录系统
- ✅ 正常使用所有功能

---

**重要提醒**: 前端代码修改后必须重启前端服务才能生效！
