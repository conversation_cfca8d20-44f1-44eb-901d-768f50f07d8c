"""
简化的用户存储机制
使用JSON文件存储用户数据，避免复杂的数据库依赖
"""
import json
import os
from typing import Dict, Optional, List
from datetime import datetime
from .security import get_password_hash, verify_password


class UserStorage:
    """简化的用户存储类"""
    
    def __init__(self, storage_file: str = "users.json"):
        self.storage_file = storage_file
        self.users_data = self._load_users()
        
        # 初始化默认用户
        self._init_default_users()
    
    def _load_users(self) -> Dict:
        """从文件加载用户数据"""
        if os.path.exists(self.storage_file):
            try:
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return {}
        return {}
    
    def _save_users(self):
        """保存用户数据到文件"""
        try:
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(self.users_data, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"Error saving users: {e}")
    
    def _init_default_users(self):
        """初始化默认用户"""
        default_users = [
            ("admin", "admin123", "<EMAIL>", "Administrator"),
            ("test", "test123", "<EMAIL>", "Test User"),
            ("user", "user123", "<EMAIL>", "Regular User"),
            ("demo", "demo123", "<EMAIL>", "Demo User"),
            ("guest", "guest123", "<EMAIL>", "Guest User")
        ]
        
        for username, password, email, full_name in default_users:
            if username not in self.users_data:
                self.create_user(username, email, password, full_name, save=False)
        
        # 保存一次
        self._save_users()
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: Optional[str] = None, save: bool = True) -> bool:
        """创建新用户"""
        # 检查用户是否已存在
        if self.user_exists(username, email):
            return False
        
        # 创建用户数据
        user_data = {
            "id": len(self.users_data) + 1,
            "username": username,
            "email": email,
            "full_name": full_name or username,
            "hashed_password": get_password_hash(password),
            "is_active": True,
            "is_superuser": username == "admin",
            "created_at": datetime.now().isoformat(),
            "last_login": None
        }
        
        self.users_data[username] = user_data
        
        if save:
            self._save_users()
        
        return True
    
    def user_exists(self, username: str, email: str = None) -> bool:
        """检查用户是否存在"""
        # 检查用户名
        if username in self.users_data:
            return True
        
        # 检查邮箱
        if email:
            for user_data in self.users_data.values():
                if user_data.get("email") == email:
                    return True
        
        return False
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """验证用户登录"""
        if username not in self.users_data:
            return None
        
        user_data = self.users_data[username]
        
        # 验证密码
        if not verify_password(password, user_data["hashed_password"]):
            return None
        
        # 更新最后登录时间
        user_data["last_login"] = datetime.now().isoformat()
        self._save_users()
        
        # 返回用户信息（不包含密码）
        return {
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "full_name": user_data["full_name"],
            "is_active": user_data["is_active"],
            "is_superuser": user_data.get("is_superuser", False),
            "created_at": user_data["created_at"],
            "last_login": user_data["last_login"]
        }
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户信息"""
        if username not in self.users_data:
            return None
        
        user_data = self.users_data[username]
        return {
            "id": user_data["id"],
            "username": user_data["username"],
            "email": user_data["email"],
            "full_name": user_data["full_name"],
            "is_active": user_data["is_active"],
            "is_superuser": user_data.get("is_superuser", False),
            "created_at": user_data["created_at"],
            "last_login": user_data["last_login"]
        }
    
    def get_all_users(self) -> List[Dict]:
        """获取所有用户信息"""
        return [self.get_user_by_username(username) for username in self.users_data.keys()]
    
    def update_user(self, username: str, **kwargs) -> bool:
        """更新用户信息"""
        if username not in self.users_data:
            return False
        
        user_data = self.users_data[username]
        
        # 允许更新的字段
        allowed_fields = ["email", "full_name", "is_active"]
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                user_data[field] = value
        
        self._save_users()
        return True
    
    def delete_user(self, username: str) -> bool:
        """删除用户"""
        if username not in self.users_data:
            return False
        
        # 不允许删除admin用户
        if username == "admin":
            return False
        
        del self.users_data[username]
        self._save_users()
        return True


# 全局用户存储实例
user_storage = UserStorage()


def get_user_storage() -> UserStorage:
    """获取用户存储实例"""
    return user_storage
