#!/usr/bin/env python3
"""
测试注册登录功能的脚本
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.core.user_storage import get_user_storage

def test_user_storage():
    """测试用户存储功能"""
    print("=== 测试用户存储功能 ===")
    
    # 获取用户存储实例
    storage = get_user_storage()
    print("✓ 用户存储初始化成功")
    
    # 显示默认用户
    print(f"✓ 默认用户: {list(storage.users_data.keys())}")
    
    # 测试注册新用户
    print("\n--- 测试用户注册 ---")
    test_username = "testuser"
    test_email = "<EMAIL>"
    test_password = "testpass123"
    
    # 先删除测试用户（如果存在）
    if test_username in storage.users_data:
        storage.delete_user(test_username)
        print(f"✓ 删除已存在的测试用户: {test_username}")
    
    # 注册新用户
    success = storage.create_user(test_username, test_email, test_password, "Test User")
    if success:
        print(f"✓ 用户注册成功: {test_username}")
    else:
        print(f"✗ 用户注册失败: {test_username}")
        return False
    
    # 测试重复注册
    success = storage.create_user(test_username, test_email, test_password, "Test User")
    if not success:
        print("✓ 重复注册被正确拒绝")
    else:
        print("✗ 重复注册应该被拒绝")
    
    # 测试登录
    print("\n--- 测试用户登录 ---")
    
    # 正确的用户名密码
    user = storage.authenticate_user(test_username, test_password)
    if user:
        print(f"✓ 登录成功: {user['username']} ({user['email']})")
    else:
        print("✗ 登录失败")
        return False
    
    # 错误的密码
    user = storage.authenticate_user(test_username, "wrongpassword")
    if not user:
        print("✓ 错误密码被正确拒绝")
    else:
        print("✗ 错误密码应该被拒绝")
    
    # 不存在的用户
    user = storage.authenticate_user("nonexistent", "password")
    if not user:
        print("✓ 不存在的用户被正确拒绝")
    else:
        print("✗ 不存在的用户应该被拒绝")
    
    # 测试默认用户登录
    print("\n--- 测试默认用户登录 ---")
    default_users = [
        ("admin", "admin123"),
        ("test", "test123"),
        ("user", "user123")
    ]
    
    for username, password in default_users:
        user = storage.authenticate_user(username, password)
        if user:
            print(f"✓ 默认用户登录成功: {username}")
        else:
            print(f"✗ 默认用户登录失败: {username}")
    
    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    try:
        success = test_user_storage()
        if success:
            print("\n🎉 所有测试通过！注册登录功能正常工作。")
        else:
            print("\n❌ 测试失败，请检查代码。")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
